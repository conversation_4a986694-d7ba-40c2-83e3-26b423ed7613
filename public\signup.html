<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Sports Malta</title>
    <meta name="description" content="Create your Sports Malta account to book sports facilities across Malta.">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/auth.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="auth-body">
    <!-- Header -->
    <header class="header">
        <nav class="nav">
            <div class="nav-container">
                <div class="nav-logo">
                    <a href="index.html">
                        <span class="logo-text">SM</span>
                        <span class="logo-subtitle">Sports Malta</span>
                    </a>
                </div>
                
                <div class="nav-menu" id="nav-menu">
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="index.html" class="nav-link">Home</a>
                        </li>
                        <li class="nav-item">
                            <a href="booking.html" class="nav-link">Book Now</a>
                        </li>
                        <li class="nav-item">
                            <a href="contact.html" class="nav-link">Contact</a>
                        </li>
                        <li class="nav-item">
                            <a href="login.html" class="nav-link">Login</a>
                        </li>
                    </ul>
                </div>
                
                <div class="nav-toggle" id="nav-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="auth-main">
        <div class="auth-container">
            <div class="auth-card">
                <div class="auth-header">
                    <h1 class="auth-title">Create Account</h1>
                    <p class="auth-subtitle">Join Sports Malta and start booking today</p>
                </div>

                <!-- Signup Form -->
                <form class="auth-form" id="signupForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstName" class="form-label">First Name</label>
                            <input 
                                type="text" 
                                id="firstName" 
                                name="firstName" 
                                class="form-input" 
                                placeholder="Enter your first name"
                                required
                                autocomplete="given-name"
                            >
                            <div class="form-error" id="firstName-error"></div>
                        </div>

                        <div class="form-group">
                            <label for="lastName" class="form-label">Last Name</label>
                            <input 
                                type="text" 
                                id="lastName" 
                                name="lastName" 
                                class="form-input" 
                                placeholder="Enter your last name"
                                required
                                autocomplete="family-name"
                            >
                            <div class="form-error" id="lastName-error"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="email" class="form-label">Email Address</label>
                        <input 
                            type="email" 
                            id="email" 
                            name="email" 
                            class="form-input" 
                            placeholder="Enter your email"
                            required
                            autocomplete="email"
                        >
                        <div class="form-error" id="email-error"></div>
                    </div>

                    <div class="form-group">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input 
                            type="tel" 
                            id="phone" 
                            name="phone" 
                            class="form-input" 
                            placeholder="+356 1234 5678"
                            required
                            autocomplete="tel"
                        >
                        <div class="form-error" id="phone-error"></div>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">Password</label>
                        <div class="password-input-container">
                            <input 
                                type="password" 
                                id="password" 
                                name="password" 
                                class="form-input" 
                                placeholder="Create a strong password"
                                required
                                autocomplete="new-password"
                            >
                            <button type="button" class="password-toggle" id="passwordToggle">
                                <span class="password-toggle-icon">👁️</span>
                            </button>
                        </div>
                        <div class="password-requirements">
                            <p class="password-requirement" id="length-req">At least 6 characters</p>
                        </div>
                        <div class="form-error" id="password-error"></div>
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword" class="form-label">Confirm Password</label>
                        <div class="password-input-container">
                            <input 
                                type="password" 
                                id="confirmPassword" 
                                name="confirmPassword" 
                                class="form-input" 
                                placeholder="Confirm your password"
                                required
                                autocomplete="new-password"
                            >
                            <button type="button" class="password-toggle" id="confirmPasswordToggle">
                                <span class="password-toggle-icon">👁️</span>
                            </button>
                        </div>
                        <div class="form-error" id="confirmPassword-error"></div>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" id="agreeTerms" name="agreeTerms" required>
                            <span class="checkbox-checkmark"></span>
                            <span class="checkbox-label">
                                I agree to the <a href="terms.html" target="_blank" class="terms-link">Terms of Service</a> 
                                and <a href="privacy.html" target="_blank" class="terms-link">Privacy Policy</a>
                            </span>
                        </label>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" id="newsletter" name="newsletter">
                            <span class="checkbox-checkmark"></span>
                            <span class="checkbox-label">
                                Send me updates and special offers via email
                            </span>
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary btn-full" id="signupButton">
                        <span class="btn-text">Create Account</span>
                        <span class="btn-loading" style="display: none;">
                            <span class="loading-spinner"></span>
                            Creating account...
                        </span>
                    </button>

                    <div class="form-error" id="form-error"></div>
                    <div class="form-success" id="form-success"></div>
                </form>

                <!-- Social Login -->
                <div class="social-login">
                    <div class="social-divider">
                        <span class="social-divider-text">or sign up with</span>
                    </div>
                    <div class="social-buttons">
                        <button type="button" class="btn btn-social" id="googleSignup">
                            <span class="social-icon">🔍</span>
                            Google
                        </button>
                        <button type="button" class="btn btn-social" id="facebookSignup">
                            <span class="social-icon">📘</span>
                            Facebook
                        </button>
                    </div>
                </div>

                <!-- Login Link -->
                <div class="auth-footer">
                    <p class="auth-footer-text">
                        Already have an account? 
                        <a href="login.html" class="auth-link">Sign in</a>
                    </p>
                </div>
            </div>

            <!-- Features Section -->
            <div class="auth-features">
                <h2 class="features-title">Join Thousands of Sports Enthusiasts</h2>
                <div class="features-list">
                    <div class="feature-item">
                        <div class="feature-icon">🎯</div>
                        <div class="feature-content">
                            <h3 class="feature-title">Instant Booking</h3>
                            <p class="feature-description">Book your favorite sports venues instantly with real-time availability</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">🔒</div>
                        <div class="feature-content">
                            <h3 class="feature-title">Secure & Safe</h3>
                            <p class="feature-description">Your data is protected with enterprise-grade security</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">📊</div>
                        <div class="feature-content">
                            <h3 class="feature-title">Track Your Activity</h3>
                            <p class="feature-description">Monitor your bookings, expenses, and sports activity</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">🎁</div>
                        <div class="feature-content">
                            <h3 class="feature-title">Exclusive Offers</h3>
                            <p class="feature-description">Get access to member-only discounts and promotions</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <span class="logo-text">SM</span>
                        <span class="logo-subtitle">Sports Malta</span>
                    </div>
                    <p class="footer-description">Your premier destination for booking sports facilities across Malta.</p>
                </div>
                
                <div class="footer-section">
                    <h3 class="footer-title">Quick Links</h3>
                    <ul class="footer-links">
                        <li><a href="booking.html">Book Now</a></li>
                        <li><a href="contact.html">Contact Us</a></li>
                        <li><a href="login.html">Login</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3 class="footer-title">Contact</h3>
                    <div class="contact-info">
                        <p>📧 <EMAIL></p>
                        <p>📞 +356 1234 5678</p>
                        <p>📍 Malta</p>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Sports Malta. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/auth.js"></script>
    <script>
        // Initialize signup page
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is already logged in
            if (window.authHandler && window.authHandler.isAuthenticated()) {
                window.location.href = '/account.html';
                return;
            }
            
            // Initialize signup form
            window.authHandler.initSignupForm();
        });
    </script>
</body>
</html>
